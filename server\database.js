// 花砖物语排位赛数据库管理器 (JSON文件存储)
const fs = require('fs').promises;
const path = require('path');

class DatabaseManager {
  constructor() {
    this.dataPath = path.join(__dirname, 'data');
    this.users = new Map();
    this.matches = new Map();
    this.ratingHistory = [];
    this.dailyStats = new Map();
    this.initialized = false;
  }

  // 初始化数据库
  async init() {
    try {
      // 创建数据目录
      await this.ensureDataDirectory();

      // 加载现有数据
      await this.loadData();

      this.initialized = true;
      console.log('✅ JSON数据库初始化成功');

      // 定期保存数据
      this.startAutoSave();

    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }

  // 确保数据目录存在
  async ensureDataDirectory() {
    try {
      await fs.access(this.dataPath);
    } catch {
      await fs.mkdir(this.dataPath, { recursive: true });
      console.log('创建数据目录:', this.dataPath);
    }
  }

  // 加载数据
  async loadData() {
    try {
      // 加载用户数据
      const usersFile = path.join(this.dataPath, 'users.json');
      try {
        const usersData = await fs.readFile(usersFile, 'utf8');
        const users = JSON.parse(usersData);
        this.users = new Map(Object.entries(users));
        console.log(`加载了 ${this.users.size} 个用户`);
      } catch {
        console.log('用户数据文件不存在，创建新的');
        this.users = new Map();
      }

      // 加载比赛数据
      const matchesFile = path.join(this.dataPath, 'matches.json');
      try {
        const matchesData = await fs.readFile(matchesFile, 'utf8');
        const matches = JSON.parse(matchesData);
        this.matches = new Map(Object.entries(matches));
        console.log(`加载了 ${this.matches.size} 场比赛记录`);
      } catch {
        console.log('比赛数据文件不存在，创建新的');
        this.matches = new Map();
      }

      // 加载积分历史
      const historyFile = path.join(this.dataPath, 'rating_history.json');
      try {
        const historyData = await fs.readFile(historyFile, 'utf8');
        this.ratingHistory = JSON.parse(historyData);
        console.log(`加载了 ${this.ratingHistory.length} 条积分历史`);
      } catch {
        console.log('积分历史文件不存在，创建新的');
        this.ratingHistory = [];
      }

      // 加载每日统计
      const statsFile = path.join(this.dataPath, 'daily_stats.json');
      try {
        const statsData = await fs.readFile(statsFile, 'utf8');
        const stats = JSON.parse(statsData);
        this.dailyStats = new Map(Object.entries(stats));
        console.log(`加载了 ${this.dailyStats.size} 条每日统计`);
      } catch {
        console.log('每日统计文件不存在，创建新的');
        this.dailyStats = new Map();
      }

    } catch (error) {
      console.error('加载数据失败:', error);
      throw error;
    }
  }

  // 保存数据
  async saveData() {
    try {
      // 保存用户数据
      const usersFile = path.join(this.dataPath, 'users.json');
      const usersObj = Object.fromEntries(this.users);
      await fs.writeFile(usersFile, JSON.stringify(usersObj, null, 2));

      // 保存比赛数据
      const matchesFile = path.join(this.dataPath, 'matches.json');
      const matchesObj = Object.fromEntries(this.matches);
      await fs.writeFile(matchesFile, JSON.stringify(matchesObj, null, 2));

      // 保存积分历史
      const historyFile = path.join(this.dataPath, 'rating_history.json');
      await fs.writeFile(historyFile, JSON.stringify(this.ratingHistory, null, 2));

      // 保存每日统计
      const statsFile = path.join(this.dataPath, 'daily_stats.json');
      const statsObj = Object.fromEntries(this.dailyStats);
      await fs.writeFile(statsFile, JSON.stringify(statsObj, null, 2));

      console.log('数据保存成功');
    } catch (error) {
      console.error('保存数据失败:', error);
      throw error;
    }
  }

  // 启动自动保存
  startAutoSave() {
    // 每5分钟自动保存一次
    setInterval(() => {
      this.saveData().catch(console.error);
    }, 5 * 60 * 1000);

    // 进程退出时保存数据
    process.on('SIGINT', async () => {
      console.log('正在保存数据...');
      await this.saveData();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('正在保存数据...');
      await this.saveData();
      process.exit(0);
    });
  }

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 获取当前日期字符串
  getCurrentDate() {
    return new Date().toISOString().split('T')[0];
  }

  // 获取当前时间戳
  getCurrentTimestamp() {
    return new Date().toISOString();
  }

  // 获取或创建用户
  async getOrCreateUser(openid, userInfo) {
    try {
      let user = this.users.get(openid);

      if (!user) {
        // 用户不存在，创建新用户
        user = {
          id: this.generateId(),
          openid: openid,
          nickname: userInfo.nickName || '玩家',
          avatar_url: userInfo.avatarUrl || '',
          rating: 1200,
          rank_tier: 'bronze',
          rank_division: 5,
          total_games: 0,
          wins: 0,
          losses: 0,
          win_rate: 0.0,
          highest_rating: 1200,
          current_streak: 0,
          longest_win_streak: 0,
          created_at: this.getCurrentTimestamp(),
          updated_at: this.getCurrentTimestamp()
        };

        this.users.set(openid, user);
        console.log('创建新用户:', openid, userInfo.nickName);
      } else {
        // 更新用户信息
        user.nickname = userInfo.nickName || user.nickname;
        user.avatar_url = userInfo.avatarUrl || user.avatar_url;
        user.updated_at = this.getCurrentTimestamp();

        // 临时修复：为测试玩家提供真实的头像URL
        if (user.nickname.includes('测试玩家') && (!user.avatar_url || user.avatar_url.includes('example.com'))) {
          const testAvatars = [
            'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxCqRzuYWQmpwiaqQEjNxbC7HaJaZTGVadSRHFKHMNYFGHaVm5WVoicWCqFqXYZxHMqS9XiaQOyJJaw/132',
            'https://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83eoj0hHXhgJNOTSOFsS4uZs8x1ConecaVOB8eIl115xmJZcT4oCicvia7wMEufibKtTLqmXSoXBZjcLRw/132',
            'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uJ3Hk4UESX2oqkekiSFPref5KVokCicxNKiaTHuibKVicWEUskLWCBhshzwjfDOYmBLvxuze7Xtxqg/132'
          ];
          const playerNumber = parseInt(user.nickname.match(/\d+/)?.[0] || '1');
          user.avatar_url = testAvatars[(playerNumber - 1) % testAvatars.length];
          console.log(`🔧 为${user.nickname}设置测试头像: ${user.avatar_url}`);
        }
      }

      return user;
    } catch (error) {
      console.error('获取或创建用户失败:', error);
      throw error;
    }
  }

  // 更新用户积分
  async updateUserRating(openid, newRating, newTier, newDivision) {
    try {
      const user = this.users.get(openid);
      if (user) {
        user.rating = newRating;
        user.rank_tier = newTier;
        user.rank_division = newDivision;
        user.highest_rating = Math.max(user.highest_rating, newRating);
        user.updated_at = this.getCurrentTimestamp();
      }
    } catch (error) {
      console.error('更新用户积分失败:', error);
      throw error;
    }
  }

  // 记录比赛结果
  async recordMatch(matchData) {
    try {
      const { matchId, players, winnerId, duration, finalScores, ratingChanges } = matchData;

      const match = {
        id: this.generateId(),
        match_id: matchId,
        players: players,
        winner_id: winnerId,
        match_duration: duration,
        final_scores: finalScores,
        rating_changes: ratingChanges,
        match_type: 'ranked',
        created_at: this.getCurrentTimestamp()
      };

      this.matches.set(matchId, match);
      console.log('比赛记录保存成功:', matchId);
    } catch (error) {
      console.error('记录比赛失败:', error);
      throw error;
    }
  }

  // 记录玩家积分历史
  async recordRatingHistory(historyData) {
    try {
      const { openid, matchId, oldRating, newRating, oldTier, newTier, placement, finalScore } = historyData;

      const history = {
        id: this.generateId(),
        openid: openid,
        match_id: matchId,
        old_rating: oldRating,
        new_rating: newRating,
        rating_change: newRating - oldRating,
        old_tier: oldTier,
        new_tier: newTier,
        placement: placement,
        final_score: finalScore,
        created_at: this.getCurrentTimestamp()
      };

      this.ratingHistory.push(history);
    } catch (error) {
      console.error('记录积分历史失败:', error);
      throw error;
    }
  }

  // 获取排行榜
  async getLeaderboard(limit = 100, offset = 0) {
    try {
      const users = Array.from(this.users.values())
        .filter(user => user.total_games > 0)
        .sort((a, b) => {
          if (b.rating !== a.rating) return b.rating - a.rating;
          return b.total_games - a.total_games;
        })
        .slice(offset, offset + limit)
        .map(user => ({
          openid: user.openid,
          nickname: user.nickname,
          rating: user.rating,
          rank_tier: user.rank_tier,
          rank_division: user.rank_division,
          total_games: user.total_games,
          wins: user.wins,
          losses: user.losses,
          win_rate: user.win_rate,
          current_streak: user.current_streak
        }));

      return users;
    } catch (error) {
      console.error('获取排行榜失败:', error);
      throw error;
    }
  }

  // 获取用户排名
  async getUserRank(openid) {
    try {
      const user = this.users.get(openid);
      if (!user) return null;

      const users = Array.from(this.users.values())
        .filter(u => u.total_games > 0)
        .sort((a, b) => b.rating - a.rating);

      const rank = users.findIndex(u => u.openid === openid) + 1;
      return rank > 0 ? rank : null;
    } catch (error) {
      console.error('获取用户排名失败:', error);
      throw error;
    }
  }

  // 更新用户游戏统计
  async updateUserStats(openid, won, ratingChange) {
    try {
      const user = this.users.get(openid);
      if (user) {
        user.total_games++;
        if (won) {
          user.wins++;
          user.current_streak++;
          user.longest_win_streak = Math.max(user.longest_win_streak, user.current_streak);
        } else {
          user.losses++;
          user.current_streak = 0;
        }
        user.win_rate = user.total_games > 0 ? user.wins / user.total_games : 0;
        user.updated_at = this.getCurrentTimestamp();

        // 更新每日统计
        await this.updateDailyStats(openid, won, ratingChange);
      }
    } catch (error) {
      console.error('更新用户统计失败:', error);
      throw error;
    }
  }

  // 更新每日统计
  async updateDailyStats(openid, won, ratingChange) {
    try {
      const today = this.getCurrentDate();
      const key = `${openid}_${today}`;

      let stats = this.dailyStats.get(key);
      if (!stats) {
        stats = {
          id: this.generateId(),
          openid: openid,
          date: today,
          games_played: 0,
          games_won: 0,
          rating_gained: 0,
          created_at: this.getCurrentTimestamp()
        };
      }

      stats.games_played++;
      if (won) stats.games_won++;
      stats.rating_gained += ratingChange;

      this.dailyStats.set(key, stats);
    } catch (error) {
      console.error('更新每日统计失败:', error);
      throw error;
    }
  }

  // 获取用户历史记录
  async getUserHistory(openid, limit = 20) {
    try {
      return this.ratingHistory
        .filter(h => h.openid === openid)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, limit);
    } catch (error) {
      console.error('获取用户历史失败:', error);
      throw error;
    }
  }

  // 关闭数据库连接
  async close() {
    try {
      await this.saveData();
      console.log('数据库已关闭');
    } catch (error) {
      console.error('关闭数据库失败:', error);
    }
  }
}

module.exports = DatabaseManager;
