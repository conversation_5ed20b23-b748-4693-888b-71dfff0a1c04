// 花砖物语排位匹配系统
const WebSocket = require('ws');

class MatchmakingSystem {
  constructor(rankingSystem, databaseManager, gameServer) {
    this.rankingSystem = rankingSystem;
    this.db = databaseManager;
    this.gameServer = gameServer; // 游戏服务器引用

    // 匹配队列
    this.matchQueue = new Map(); // playerId -> queueEntry
    this.activeMatches = new Map(); // matchId -> matchData
    
    // 匹配参数
    this.MATCH_SIZE = 4; // 4人对战
    this.MAX_WAIT_TIME = 300; // 最大等待时间5分钟
    this.MATCH_CHECK_INTERVAL = 5000; // 每5秒检查一次匹配
    this.QUEUE_TIMEOUT = 600; // 队列超时10分钟
    
    // 启动匹配检查定时器
    this.startMatchmaking();
  }

  // 加入排位队列
  async joinQueue(playerId, playerInfo, ws) {
    try {
      // 检查玩家是否已在队列中
      if (this.matchQueue.has(playerId)) {
        throw new Error('玩家已在匹配队列中');
      }

      // 获取玩家数据
      const user = await this.db.getOrCreateUser(playerId, playerInfo);
      
      const queueEntry = {
        playerId: playerId,
        playerInfo: playerInfo,
        rating: user.rating,
        tier: user.rank_tier,
        division: user.rank_division,
        ws: ws,
        joinTime: Date.now(),
        lastUpdate: Date.now(),
        searchRange: this.rankingSystem.getMatchingRange(user.rating, 0)
      };

      this.matchQueue.set(playerId, queueEntry);
      
      console.log(`玩家 ${playerInfo.nickName} (${user.rating}分) 加入排位队列`);
      
      // 通知客户端加入队列成功
      this.sendToPlayer(playerId, 'queueJoined', {
        position: this.matchQueue.size,
        estimatedWaitTime: this.estimateWaitTime(user.rating)
      });

      // 立即尝试匹配
      this.tryCreateMatch();
      
      return true;
    } catch (error) {
      console.error('加入队列失败:', error);
      throw error;
    }
  }

  // 离开排位队列
  leaveQueue(playerId) {
    const queueEntry = this.matchQueue.get(playerId);
    if (queueEntry) {
      this.matchQueue.delete(playerId);
      console.log(`玩家 ${playerId} 离开排位队列`);
      
      this.sendToPlayer(playerId, 'queueLeft', {});
      return true;
    }
    return false;
  }

  // 启动匹配系统
  startMatchmaking() {
    // 定期检查匹配
    setInterval(() => {
      this.tryCreateMatch();
      this.cleanupQueue();
    }, this.MATCH_CHECK_INTERVAL);

    // 定期更新队列状态
    setInterval(() => {
      this.updateQueueStatus();
    }, 10000); // 每10秒更新一次

    console.log('✅ 排位匹配系统启动');
  }

  // 尝试创建匹配
  tryCreateMatch() {
    if (this.matchQueue.size < this.MATCH_SIZE) {
      return; // 队列人数不足
    }

    const queueArray = Array.from(this.matchQueue.values());
    
    // 按积分排序
    queueArray.sort((a, b) => a.rating - b.rating);

    // 尝试找到合适的匹配组合
    for (let i = 0; i <= queueArray.length - this.MATCH_SIZE; i++) {
      const potentialMatch = queueArray.slice(i, i + this.MATCH_SIZE);
      
      if (this.isValidMatch(potentialMatch)) {
        this.createMatch(potentialMatch);
        return;
      }
    }

    // 如果没有找到完美匹配，尝试扩大搜索范围
    this.expandSearchRanges();
  }

  // 检查是否为有效匹配
  isValidMatch(players) {
    if (players.length !== this.MATCH_SIZE) {
      return false;
    }

    const ratings = players.map(p => p.rating);
    const minRating = Math.min(...ratings);
    const maxRating = Math.max(...ratings);
    const ratingSpread = maxRating - minRating;

    // 检查积分差距
    const maxAllowedSpread = this.getMaxRatingSpread(players);
    if (ratingSpread > maxAllowedSpread) {
      return false;
    }

    // 检查等待时间
    const now = Date.now();
    const hasLongWaiter = players.some(p => (now - p.joinTime) > 60000); // 有人等待超过1分钟
    
    if (hasLongWaiter && ratingSpread <= maxAllowedSpread * 1.5) {
      return true; // 长时间等待的玩家可以接受更大的积分差距
    }

    return ratingSpread <= maxAllowedSpread;
  }

  // 获取最大允许的积分差距
  getMaxRatingSpread(players) {
    const avgRating = players.reduce((sum, p) => sum + p.rating, 0) / players.length;
    const maxWaitTime = Math.max(...players.map(p => Date.now() - p.joinTime));
    
    let baseSpread = 150;
    
    // 高分段收紧匹配
    if (avgRating >= 1700) baseSpread = 200;
    else if (avgRating >= 1500) baseSpread = 175;
    else if (avgRating >= 1300) baseSpread = 150;
    else baseSpread = 125;

    // 根据等待时间放宽限制
    const timeBonus = Math.floor(maxWaitTime / 30000) * 25; // 每30秒增加25分差距
    
    return Math.min(baseSpread + timeBonus, 400);
  }

  // 创建匹配
  async createMatch(players) {
    try {
      const matchId = this.generateMatchId();

      // 先通知所有玩家匹配成功（在移除队列之前）
      const matchData = {
        matchId: matchId,
        players: players.map(p => ({
          id: p.playerId,
          nickName: p.playerInfo.nickName,
          avatarUrl: p.playerInfo.avatarUrl,
          rating: p.rating,
          tier: p.tier,
          division: p.division,
          ws: p.ws // 保存WebSocket连接
        })),
        status: 'created',
        createTime: Date.now(),
        quality: this.rankingSystem.calculateMatchQuality(players)
      };

      console.log(`创建排位匹配 ${matchId}:`, players.map(p =>
        `${p.playerInfo.nickName}(${p.rating})`
      ).join(', '));

      // 通知所有玩家匹配成功（在移除队列之前发送）
      players.forEach(player => {
        this.sendToPlayer(player.playerId, 'matchFound', {
          matchId: matchId,
          players: matchData.players,
          quality: matchData.quality,
          estimatedStartTime: Date.now() + 10000 // 10秒后开始
        });
      });

      // 然后从队列中移除这些玩家
      players.forEach(player => {
        this.matchQueue.delete(player.playerId);
      });

      this.activeMatches.set(matchId, matchData);

      // 10秒后自动开始游戏
      setTimeout(() => {
        this.startRankedMatch(matchId);
      }, 10000);

      return matchId;
    } catch (error) {
      console.error('创建匹配失败:', error);
      // 将玩家重新加入队列
      players.forEach(player => {
        this.matchQueue.set(player.playerId, player);
      });
      throw error;
    }
  }

  // 开始排位赛
  async startRankedMatch(matchId) {
    try {
      const matchData = this.activeMatches.get(matchId);
      if (!matchData) {
        console.error('匹配不存在:', matchId);
        return;
      }

      matchData.status = 'starting';
      matchData.startTime = Date.now();

      // 创建初始游戏状态
      const gameState = this.createInitialGameState(matchData.players);
      matchData.gameState = gameState;

      // 在游戏服务器中创建排位赛房间
      if (this.gameServer && this.gameServer.createRankedRoom) {
        this.gameServer.createRankedRoom(matchId, matchData);
      }

      // 通知所有玩家游戏开始（直接使用WebSocket发送）
      matchData.players.forEach(player => {
        // 获取玩家的WebSocket连接
        const playerConnection = this.getPlayerConnection(player.id);
        if (playerConnection && playerConnection.readyState === 1) {
          playerConnection.send(JSON.stringify({
            type: 'rankedGameStarting',
            data: {
              matchId: matchId,
              gameMode: 'ranked',
              players: matchData.players,
              gameState: gameState
            }
          }));
        } else {
          console.warn(`无法发送游戏开始消息给玩家 ${player.id}，连接不可用`);
        }
      });

      console.log(`排位赛 ${matchId} 开始`);
    } catch (error) {
      console.error('开始排位赛失败:', error);
    }
  }

  // 获取玩家的WebSocket连接
  getPlayerConnection(playerId) {
    // 首先检查队列中是否有该玩家
    const queueEntry = this.matchQueue.get(playerId);
    if (queueEntry && queueEntry.ws && queueEntry.ws.readyState === 1) {
      return queueEntry.ws;
    }

    // 如果不在队列中，检查活跃匹配中的玩家
    for (const match of this.activeMatches.values()) {
      const player = match.players.find(p => p.id === playerId);
      if (player && player.ws && player.ws.readyState === 1) {
        return player.ws;
      }
    }

    return null;
  }

  // 完成排位赛
  async completeRankedMatch(matchId, gameResult) {
    try {
      const matchData = this.activeMatches.get(matchId);
      if (!matchData) {
        console.error('匹配不存在:', matchId);
        return;
      }

      matchData.status = 'completed';
      matchData.endTime = Date.now();
      matchData.duration = matchData.endTime - matchData.startTime;
      matchData.result = gameResult;

      // 计算积分变化
      const ratingChanges = this.rankingSystem.calculateRatingChanges(
        matchData.players,
        gameResult.finalScores
      );

      // 更新数据库
      await this.updatePlayerRatings(matchId, matchData, ratingChanges);

      // 通知玩家结果
      matchData.players.forEach((player, index) => {
        const change = ratingChanges[index];
        this.sendToPlayer(player.id, 'rankedGameCompleted', {
          matchId: matchId,
          result: gameResult,
          ratingChange: change,
          newRating: change.newRating,
          newTier: change.newTier,
          allPlayersRatingChanges: ratingChanges // 添加所有玩家的积分变化
        });
      });

      // 清理匹配数据
      setTimeout(() => {
        this.activeMatches.delete(matchId);
      }, 300000); // 5分钟后清理

      console.log(`排位赛 ${matchId} 完成，积分变化:`, ratingChanges.map(c => 
        `${c.playerId}: ${c.ratingChange > 0 ? '+' : ''}${c.ratingChange}`
      ).join(', '));

    } catch (error) {
      console.error('完成排位赛失败:', error);
    }
  }

  // 更新玩家积分
  async updatePlayerRatings(matchId, matchData, ratingChanges) {
    try {
      // 记录比赛
      await this.db.recordMatch({
        matchId: matchId,
        players: matchData.players,
        winnerId: matchData.players[0].id, // 第一名
        duration: Math.floor(matchData.duration / 1000),
        finalScores: matchData.result.finalScores,
        ratingChanges: ratingChanges
      });

      // 更新每个玩家的积分和统计
      for (let i = 0; i < matchData.players.length; i++) {
        const player = matchData.players[i];
        const change = ratingChanges[i];

        // 更新用户积分
        await this.db.updateUserRating(
          player.id,
          change.newRating,
          change.newTier.tier,
          change.newTier.division
        );

        // 更新用户游戏统计
        await this.db.updateUserStats(
          player.id,
          change.placement === 1, // 第一名为胜利
          change.ratingChange
        );

        // 记录积分历史
        await this.db.recordRatingHistory({
          openid: player.id,
          matchId: matchId,
          oldRating: change.oldRating,
          newRating: change.newRating,
          oldTier: `${change.oldTier.tier}_${change.oldTier.division}`,
          newTier: `${change.newTier.tier}_${change.newTier.division}`,
          placement: change.placement,
          finalScore: change.finalScore
        });
      }
    } catch (error) {
      console.error('更新玩家积分失败:', error);
      throw error;
    }
  }

  // 扩大搜索范围
  expandSearchRanges() {
    const now = Date.now();
    for (const [playerId, queueEntry] of this.matchQueue) {
      const waitTime = (now - queueEntry.joinTime) / 1000;
      queueEntry.searchRange = this.rankingSystem.getMatchingRange(queueEntry.rating, waitTime);
    }
  }

  // 更新队列状态
  updateQueueStatus() {
    for (const [playerId, queueEntry] of this.matchQueue) {
      const waitTime = Date.now() - queueEntry.joinTime;
      const position = this.getQueuePosition(playerId);
      
      this.sendToPlayer(playerId, 'queueUpdate', {
        position: position,
        waitTime: Math.floor(waitTime / 1000),
        estimatedWaitTime: this.estimateWaitTime(queueEntry.rating)
      });
    }
  }

  // 清理队列
  cleanupQueue() {
    const now = Date.now();
    const toRemove = [];

    for (const [playerId, queueEntry] of this.matchQueue) {
      // 移除超时的队列项
      if (now - queueEntry.joinTime > this.QUEUE_TIMEOUT * 1000) {
        console.log(`玩家 ${playerId} 队列超时，移除`);
        toRemove.push(playerId);
        continue;
      }

      // 检查连接状态 - 增加容错性
      if (!queueEntry.ws) {
        console.log(`玩家 ${playerId} WebSocket连接为空，移除`);
        toRemove.push(playerId);
        continue;
      }

      // 只有在连接明确关闭时才移除，对于其他状态给予容错
      if (queueEntry.ws.readyState === 3) { // CLOSED
        console.log(`玩家 ${playerId} WebSocket连接已关闭，移除`);
        toRemove.push(playerId);
        continue;
      }

      // 注释掉心跳检查，因为WebSocket连接状态检查已经足够
      // WebSocket连接状态比心跳更可靠，避免误判
      // if (queueEntry.lastUpdate && (now - queueEntry.lastUpdate > 30000)) {
      //   console.log(`玩家 ${playerId} 心跳超时，移除`);
      //   toRemove.push(playerId);
      //   continue;
      // }
    }

    if (toRemove.length > 0) {
      console.log(`清理队列，移除 ${toRemove.length} 个玩家:`, toRemove);
      toRemove.forEach(playerId => {
        this.leaveQueue(playerId);
      });
    }
  }

  // 估算等待时间
  estimateWaitTime(rating) {
    const queueSize = this.matchQueue.size;
    const baseTime = Math.max(30, 120 - queueSize * 10); // 基础等待时间
    
    // 高分段等待时间更长
    let tierMultiplier = 1;
    if (rating >= 1700) tierMultiplier = 2.0;
    else if (rating >= 1500) tierMultiplier = 1.5;
    else if (rating >= 1300) tierMultiplier = 1.2;
    
    return Math.floor(baseTime * tierMultiplier);
  }

  // 获取队列位置
  getQueuePosition(playerId) {
    const queueArray = Array.from(this.matchQueue.values());
    queueArray.sort((a, b) => a.joinTime - b.joinTime);
    return queueArray.findIndex(entry => entry.playerId === playerId) + 1;
  }

  // 发送消息给玩家
  sendToPlayer(playerId, type, data) {
    console.log(`🎯 尝试发送消息给玩家 ${playerId}，类型: ${type}`);

    // 首先尝试从队列中获取连接（用于队列相关消息）
    const queueEntry = this.matchQueue.get(playerId);
    if (queueEntry && queueEntry.ws && queueEntry.ws.readyState === WebSocket.OPEN) {
      console.log(`🎯 通过队列连接发送消息给玩家 ${playerId}`);
      queueEntry.ws.send(JSON.stringify({ type, data }));
      return;
    }

    // 如果队列中没有，尝试从全局连接管理器获取（用于游戏结束等消息）
    console.log(`🎯 检查全局连接管理器:`, {
      hasGameServer: !!this.gameServer,
      hasGlobalConnections: !!(this.gameServer && this.gameServer.globalConnections),
      totalPlayers: this.gameServer?.globalConnections?.players?.size || 0
    });

    if (this.gameServer && this.gameServer.globalConnections) {
      const playerConnection = this.gameServer.globalConnections.players.get(playerId);
      console.log(`🎯 玩家 ${playerId} 连接状态:`, {
        hasConnection: !!playerConnection,
        hasWs: !!(playerConnection && playerConnection.ws),
        wsReadyState: playerConnection?.ws?.readyState,
        wsReadyStateOK: playerConnection?.ws?.readyState === 1
      });

      if (playerConnection && playerConnection.ws && playerConnection.ws.readyState === WebSocket.OPEN) {
        console.log(`🎯 通过全局连接发送消息给玩家 ${playerId}`);
        playerConnection.ws.send(JSON.stringify({ type, data }));
        return;
      }
    }

    console.warn(`🎯 无法发送消息给玩家 ${playerId}：连接不存在或已断开`);
  }

  // 生成匹配ID
  generateMatchId() {
    return 'ranked_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      queueSize: this.matchQueue.size,
      activeMatches: this.activeMatches.size,
      averageWaitTime: this.calculateAverageWaitTime()
    };
  }

  // 计算平均等待时间
  calculateAverageWaitTime() {
    if (this.matchQueue.size === 0) return 0;
    
    const now = Date.now();
    const totalWaitTime = Array.from(this.matchQueue.values())
      .reduce((sum, entry) => sum + (now - entry.joinTime), 0);
    
    return Math.floor(totalWaitTime / this.matchQueue.size / 1000);
  }

  // 创建初始游戏状态
  createInitialGameState(players) {
    // 创建工厂（4人游戏需要9个工厂）
    const factories = [];
    for (let i = 0; i < 9; i++) {
      factories.push({
        id: i,
        tiles: []
      });
    }

    // 创建中央区域
    const centerArea = {
      tiles: [],
      hasFirstPlayerMarker: true
    };

    // 创建玩家状态
    const playerStates = players.map((player, index) => ({
      id: player.id,
      nickName: player.nickName,
      avatarUrl: player.avatarUrl, // 保留头像URL
      patternLines: [[], [], [], [], []],
      wall: this.createPlayerWall(),
      floorLine: [],
      score: 0,
      isFirstPlayer: index === 0
    }));

    // 创建瓷砖袋
    const tileBag = this.createTileBag();

    // 填充工厂
    this.fillFactories(factories, tileBag);

    console.log('创建的工厂数据:');
    factories.forEach((factory, index) => {
      console.log(`工厂${index}:`, factory);
    });

    return {
      players: playerStates,
      factories: factories,
      centerArea: centerArea,
      tileBag: tileBag,
      discardPile: [],
      currentPlayer: 0,
      playerCount: players.length,
      round: 1,
      phase: 'collect',
      gameEnded: false,
      firstPlayerMarker: true,
      moveHistory: []
    };
  }

  // 创建瓷砖袋
  createTileBag() {
    const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
    const tiles = [];

    // 每种颜色20个瓷砖
    colors.forEach(color => {
      for (let i = 0; i < 20; i++) {
        tiles.push(color);
      }
    });

    // 洗牌
    for (let i = tiles.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [tiles[i], tiles[j]] = [tiles[j], tiles[i]];
    }

    return tiles;
  }

  // 填充工厂
  fillFactories(factories, tileBag) {
    factories.forEach(factory => {
      factory.tiles = [];
      for (let i = 0; i < 4; i++) {
        if (tileBag.length > 0) {
          factory.tiles.push(tileBag.pop());
        }
      }
    });
  }

  // 创建玩家墙壁
  createPlayerWall() {
    // 花砖物语的墙壁模式：每行每列都有固定的颜色模式
    const colorPattern = [
      ['blue', 'yellow', 'red', 'black', 'teal'],
      ['teal', 'blue', 'yellow', 'red', 'black'],
      ['black', 'teal', 'blue', 'yellow', 'red'],
      ['red', 'black', 'teal', 'blue', 'yellow'],
      ['yellow', 'red', 'black', 'teal', 'blue']
    ];

    return colorPattern.map(row =>
      row.map(color => ({ color, filled: false }))
    );
  }
}

module.exports = MatchmakingSystem;
